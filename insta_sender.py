  



#*==========================*#
#* Import/Load Data Section *#
#*==========================*#
#region
import streamlit as st
import streamlit.components.v1 as components
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from configparser import ConfigParser
import time
import itertools
import os
import re
import pymongo
from pymongo import MongoClient
import sys
import webbrowser
cwd = os.getcwd()

# prog import - Fixed corrupted module
import prog

#endregion



#*====================*#
#* Config.ini Section *#
#*====================*#
#region
### !Loading config.ini Data/Variables ###
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get Data from [targeted page data] Section
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
my_list_followers = parser["Program_Data"]["my_list_followers"]
message = parser['Program_Data']["message"]
start_follow = int(parser['Program_Data']['start_follow'])
stop_follow = int(parser['Program_Data']['stop_follow'])
start_send = int(parser['Program_Data']['start_send'])
stop_send = int(parser['Program_Data']['stop_send'])
wait = int(parser['Program_Data']['wait'])


#! Config Dictionary
parser = ConfigParser()
parser.read("config.ini", encoding='utf-8')
config_dictionary = {}
for section in parser.sections():
    config_dictionary[section] = {}
    for option in parser.options(section):
        config_dictionary[section][option] = parser.get(section, option)

#endregion






prog.state_101()

#endregion
     



#*=======================*#
#* Page Config Functions *#
#*=======================*#
#region

### !📦Function: config_dictionary_generator ###
#!▶️▶️▶️
def config_dictionary_generator():
    parser = ConfigParser()
    parser.read("config.ini", encoding='utf-8')
    config_dictionary = {}
    for section in parser.sections():
        config_dictionary[section] = {}
        for option in parser.options(section):
            config_dictionary[section][option] = parser.get(section, option)
    return config_dictionary


### !📦Function: generate_config_sections_tuple ###
#!▶️▶️▶️
def generate_config_sections_tuple():
    parser = ConfigParser()
    parser.read("config.ini", encoding='utf-8')
    config_sections_list = parser.sections()
    config_sections_tuple = tuple(config_sections_list)
    return config_sections_tuple


### !📦Function: generate_config_section_options_tuple ###
#!▶️▶️▶️
def generate_config_section_options_tuple(section):
    parser = ConfigParser()
    parser.read("config.ini", encoding='utf-8')
    config_section_options_list = parser.options(section)
    config_section_options_tuple = tuple(config_section_options_list)
    return config_section_options_tuple










#*=======================*#
#*=======================*#
#* Streamlit GUI Section *#
#*=======================*#
#*=======================*#
#region

### !START: Primary GUI Config #######
#region
# Set Page Config: "Phantom - Anti-Detect"
st.set_page_config(
    page_title = "Insta-Sender",
    page_icon  = '🌀'
)
#endregion

#! Streamlit Remove App footer
#region 
hide_st_style = """
            <style>
            footer {visibility: hidden;}
            </style>
            """
st.markdown(hide_st_style, unsafe_allow_html=True)
#endregion


#! Streamlit sidebar #####
#region
main_menu_options = st.sidebar.selectbox(
    "Insta Sender Project",
    ("🧾 About","⚙️ Insta-Sender FREE Version", "⭐ Insta-Sender Pro [Unlimited Version]")
)
#endregion




#!===================!#
#! If 'About' Option !#
#!===================!#

#region
if main_menu_options == "🧾 About":
    with st.container():
        st.subheader("🧾 About Insta-Sender IM-BOT | Send hundreds of messages and connect with new followers NOW !!")

        st.image("./img/about_footer.png")

        st.write("Insta-Sender is an Instagram marketing software/BOT that can send messages to  targeted followers data to build your highly targeted Instagram audience for your online business.")
        st.write("By Cyborg TEAM :\n * Abdelkarim Ben Mohammadi\n* Nassima Haddadi\n * Cyborg ULTRA AI")


#endregion




#!=======================================!#
#! If 'Insta-Sender FREE Version' Option !#
#!=======================================!#
#region
if main_menu_options == "⚙️ Insta-Sender FREE Version":

    st.subheader("🌀 Launch  your Insta-Sender IM-BOT Campaign |Connect with new followers and bring them to your INSTAGRAM PAGE NOW !! ")
    col1, col2 = st.columns([1, 3])
    with col1:
        st.image("./img/insta_logo.png", width=150)
    with col2:
        st.write(" ")
        st.write(" ")
        st.write("Insta-Sender is an Instagram marketing software/BOT that can send messages to  targeted followers data to build your highly targeted Instagram audience for your online business.")

    # Section pour les identifiants Instagram
    st.subheader("🔐 Identifiants Instagram")

    with st.form("instagram_credentials"):
        col_user, col_pass = st.columns(2)

        with col_user:
            instagram_username = st.text_input("📧 Nom d'utilisateur Instagram", placeholder="votre_nom_utilisateur")

        with col_pass:
            instagram_password = st.text_input("🔒 Mot de passe Instagram", type="password", placeholder="votre_mot_de_passe")

        st.warning("⚠️ Vos identifiants sont utilisés uniquement pour la connexion automatique et ne sont pas sauvegardés.")

        # Configuration du message
        st.subheader("💬 Configuration du Message")
        custom_message = st.text_area("Message à envoyer", value=message, height=100)

        # Paramètres d'envoi
        col_start, col_stop, col_wait = st.columns(3)
        with col_start:
            start_index = st.number_input("Index de début", min_value=0, max_value=14, value=start_send)
        with col_stop:
            stop_index = st.number_input("Index de fin", min_value=1, max_value=15, value=stop_send)
        with col_wait:
            wait_seconds = st.number_input("Délai entre envois (sec)", min_value=10, max_value=60, value=wait)

        # Bouton de lancement
        run_instasender_button = st.form_submit_button("🚀 Lancer InstaSender BOT", type="primary")

        if run_instasender_button:
            if not instagram_username or not instagram_password:
                st.error("❌ Veuillez saisir vos identifiants Instagram")
            else:
                # Sauvegarder temporairement les paramètres
                st.session_state.instagram_username = instagram_username
                st.session_state.instagram_password = instagram_password
                st.session_state.custom_message = custom_message
                st.session_state.start_index = start_index
                st.session_state.stop_index = stop_index
                st.session_state.wait_seconds = wait_seconds

                # Lancer le bot
                prog.botprogram(
                    username=instagram_username,
                    password=instagram_password,
                    message=custom_message,
                    start_idx=start_index,
                    stop_idx=stop_index,
                    wait_time=wait_seconds
                )

 #endregion


#!=======================================!#
#! If 'Insta-Sender Pro [Unlimited Version]' Option !#
#!=======================================!#
#region
if main_menu_options == "⭐ Insta-Sender Pro [Unlimited Version]":

    st.title("⭐ Insta-Sender Pro Unlimited Version | Get as many followers as possible by sending as many messages as possible  | GO FASTER & Be UNLIMITED !! ")
    col3, col4, col5 = st.columns(3)
    with col4:
        st.image("./img/golden_instalogo.png", width=200)

    st.subheader("✔️ Grow your online business with HIGH-QUALITY Instagram Followers Automatically")
    st.subheader("✔️ BOOST YOUR TARGETED ORGANIC TRAFFIC FOR FREE EVERYDAY")
    st.subheader("✔️ Automate your targeted traffic generation WITHOUT ADS or PAID Camapign")
    st.subheader("✔️ Build your targetd audience on AUTOPILOTE")
    st.subheader("✔️ Get your Competitors TARGETED FOLLOWERS DATA on AUTOPILOTE and benefit from their efforts EASILY ..")
    st.subheader("✔️ BOOST your Social Media Marketing Campaigns with the Targeted DATA of your Competitors with EASE !!")
    st.subheader("✔️ Get Big Results on your Online Business With the Super-Power and the High-Speed of the Trending Ai and Data-science BOTs Technology !!")
    st.subheader("And you can get More And More BENIFITS from this BOTs technolgy ...")
    st.subheader(" ")

    col6, col7, col8 = st.columns([1, 3, 1])
    with col6:
        st.image("./img/arrow_red_right.png")
    with col7:
        get_info_button = st.button("""Contact Us NOW to Get More Info about 'Insta-Sniper Pro Unlimited Version' and all our others Instagram Traffic & Autopilote Marketing BOTs Projects !!""")
        if get_info_button :
            get_more_info_form_url = "https://forms.gle/y2NBe2cv725gJLYR9"
            webbrowser.open_new_tab(get_more_info_form_url)
    with col8:
        st.image("./img/arrow_red_left.png")




















