"""
Module prog.py - Fonctions pour le bot Instagram
Remplace le fichier prog.pyc corrompu
"""

import streamlit as st
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from configparser import Config<PERSON>arser

def state_101():
    """
    Fonction d'initialisation du programme
    """
    print("Insta-Sender initialisé avec succès!")
    return True

def setup_chrome_driver():
    """
    Configure et initialise le driver Chrome pour l'automatisation
    """
    chrome_options = Options()

    # Options pour éviter la détection de bot
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Utiliser le Chrome portable si disponible
    chrome_portable_path = "./GoogleChromePortable_107.0.5304.88_online.paf.exe"
    if os.path.exists(chrome_portable_path):
        chrome_options.binary_location = chrome_portable_path

    # Utiliser le chromedriver local
    chromedriver_path = "./chromedriver.exe"
    if os.path.exists(chromedriver_path):
        driver = webdriver.Chrome(executable_path=chromedriver_path, options=chrome_options)
    else:
        # Fallback vers le driver système
        driver = webdriver.Chrome(options=chrome_options)

    # Masquer les signaux d'automatisation
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    return driver

def login_instagram(driver, username, password):
    """
    Connexion automatique à Instagram
    """
    try:
        # Aller sur Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)

        # Accepter les cookies si nécessaire
        try:
            accept_cookies = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'Accepter')]"))
            )
            accept_cookies.click()
            time.sleep(2)
        except:
            pass

        # Trouver les champs de connexion avec plusieurs sélecteurs possibles
        try:
            username_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
        except:
            try:
                username_field = driver.find_element(By.XPATH, "//input[@aria-label='Numéro de téléphone, nom d'utilisateur ou adresse e-mail']")
            except:
                username_field = driver.find_element(By.XPATH, "//input[@placeholder='Numéro de téléphone, nom d'utilisateur ou adresse e-mail']")

        try:
            password_field = driver.find_element(By.NAME, "password")
        except:
            password_field = driver.find_element(By.XPATH, "//input[@aria-label='Mot de passe']")

        # Saisir les identifiants lentement pour éviter la détection
        username_field.clear()
        for char in username:
            username_field.send_keys(char)
            time.sleep(0.1)

        time.sleep(1)

        password_field.clear()
        for char in password:
            password_field.send_keys(char)
            time.sleep(0.1)

        time.sleep(2)

        # Cliquer sur le bouton de connexion
        try:
            login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
        except:
            login_button = driver.find_element(By.XPATH, "//div[contains(text(), 'Se connecter')]")

        login_button.click()

        # Attendre la redirection et vérifier si connecté
        time.sleep(8)

        # Vérifier si on est sur la page d'accueil
        try:
            # Chercher un élément qui indique qu'on est connecté
            WebDriverWait(driver, 10).until(
                EC.any_of(
                    EC.presence_of_element_located((By.XPATH, "//a[@href='/']")),
                    EC.presence_of_element_located((By.XPATH, "//svg[@aria-label='Accueil']")),
                    EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Accueil')]"))
                )
            )
            return True
        except:
            # Vérifier s'il y a une erreur de connexion
            try:
                error_element = driver.find_element(By.XPATH, "//*[contains(text(), 'incorrect') or contains(text(), 'erreur')]")
                st.error(f"Erreur de connexion: {error_element.text}")
                return False
            except:
                return True  # Assumer que la connexion a réussi

    except Exception as e:
        st.error(f"Erreur lors de la connexion: {str(e)}")
        return False

def send_message_to_user(driver, username, message):
    """
    Envoie un message privé à un utilisateur spécifique
    """
    try:
        # Aller sur le profil de l'utilisateur
        driver.get(f"https://www.instagram.com/{username}/")
        time.sleep(5)

        # Vérifier si le profil existe
        try:
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//h2[contains(text(), 'Cette page n'est pas disponible')]"))
            )
            st.warning(f"⚠️ Profil {username} non trouvé ou privé")
            return False
        except:
            pass  # Le profil existe

        # Chercher le bouton "Message" avec plusieurs sélecteurs possibles
        message_button = None
        selectors = [
            "//div[contains(text(), 'Message')]",
            "//button[contains(text(), 'Message')]",
            "//div[contains(text(), 'Envoyer un message')]",
            "//button[contains(text(), 'Envoyer un message')]",
            "//div[@role='button'][contains(text(), 'Message')]"
        ]

        for selector in selectors:
            try:
                message_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                break
            except:
                continue

        if not message_button:
            st.warning(f"⚠️ Bouton Message non trouvé pour {username}")
            return False

        message_button.click()
        time.sleep(3)

        # Gérer la popup "Envoyer un message" si elle apparaît
        try:
            send_message_popup = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Envoyer un message')]"))
            )
            send_message_popup.click()
            time.sleep(2)
        except:
            pass

        # Trouver la zone de texte avec plusieurs sélecteurs
        message_box = None
        text_selectors = [
            "//textarea[@placeholder='Message...']",
            "//textarea[@placeholder='Écrivez un message...']",
            "//div[@contenteditable='true'][@role='textbox']",
            "//textarea[@aria-label='Message']"
        ]

        for selector in text_selectors:
            try:
                message_box = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                break
            except:
                continue

        if not message_box:
            st.warning(f"⚠️ Zone de texte non trouvée pour {username}")
            return False

        # Saisir le message lentement
        message_box.clear()
        for char in message:
            message_box.send_keys(char)
            time.sleep(0.05)

        time.sleep(2)

        # Envoyer le message avec plusieurs sélecteurs
        send_button = None
        send_selectors = [
            "//button[contains(text(), 'Send')]",
            "//button[contains(text(), 'Envoyer')]",
            "//div[@role='button'][contains(text(), 'Envoyer')]",
            "//button[@type='submit']"
        ]

        for selector in send_selectors:
            try:
                send_button = driver.find_element(By.XPATH, selector)
                break
            except:
                continue

        if not send_button:
            st.warning(f"⚠️ Bouton Envoyer non trouvé pour {username}")
            return False

        send_button.click()
        time.sleep(3)

        # Vérifier que le message a été envoyé
        try:
            # Chercher une confirmation d'envoi
            WebDriverWait(driver, 5).until(
                EC.any_of(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Message envoyé')]")),
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Sent')]")),
                    EC.presence_of_element_located((By.XPATH, "//div[@data-testid='message-text']"))
                )
            )
            return True
        except:
            # Assumer que le message a été envoyé si pas d'erreur
            return True

    except Exception as e:
        st.error(f"Erreur lors de l'envoi du message à {username}: {str(e)}")
        return False

def botprogram(username=None, password=None, message=None, start_idx=None, stop_idx=None, wait_time=None):
    """
    Fonction principale du bot Instagram avec vraie automatisation
    """
    st.info("🤖 Démarrage du bot Instagram...")

    # Utiliser les paramètres fournis ou charger depuis config.ini
    if not all([username, password, message, start_idx is not None, stop_idx is not None, wait_time is not None]):
        # Charger la configuration par défaut
        parser = ConfigParser()
        parser.read("config.ini", encoding='utf-8')

        start_send = start_idx if start_idx is not None else int(parser['Program_Data']['start_send'])
        stop_send = stop_idx if stop_idx is not None else int(parser['Program_Data']['stop_send'])
        bot_message = message if message else parser['Program_Data']['message']
        bot_wait_time = wait_time if wait_time is not None else int(parser['Program_Data']['wait'])
        followers_file = parser['Program_Data']['my_list_followers']
    else:
        # Utiliser les paramètres fournis
        start_send = start_idx
        stop_send = stop_idx
        bot_message = message
        bot_wait_time = wait_time
        followers_file = "followers_data.txt"

    try:
        # Lire la liste des followers
        with open(followers_file, 'r', encoding='utf-8') as f:
            followers = [line.strip() for line in f.readlines()]

        # Limiter la liste selon les paramètres
        target_followers = followers[start_send:stop_send]

        st.info(f"📋 {len(target_followers)} utilisateurs ciblés pour l'envoi de messages")

        # Afficher les identifiants reçus (masqués pour la sécurité)
        if username and password:
            st.success(f"✅ Identifiants reçus pour: {username[:3]}***")
            st.info("💡 Initialisation du navigateur Chrome...")

            # Initialiser le driver Chrome
            try:
                driver = setup_chrome_driver()
                st.success("✅ Navigateur Chrome initialisé")

                # Connexion à Instagram
                st.info("🔐 Connexion à Instagram en cours...")
                if login_instagram(driver, username, password):
                    st.success("✅ Connexion à Instagram réussie")

                    # Processus d'envoi réel
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    success_count = 0
                    failed_count = 0

                    for i, follower in enumerate(target_followers):
                        status_text.text(f"📤 Envoi du message à: {follower}")
                        progress_bar.progress((i + 1) / len(target_followers))

                        # Envoi réel du message
                        if send_message_to_user(driver, follower, bot_message):
                            success_count += 1
                            st.write(f"✅ Message envoyé à {follower}")
                        else:
                            failed_count += 1
                            st.write(f"❌ Échec envoi à {follower}")

                        # Attendre entre les envois
                        if i < len(target_followers) - 1:
                            status_text.text(f"⏳ Attente de {bot_wait_time} secondes...")
                            time.sleep(bot_wait_time)

                    # Fermer le navigateur
                    driver.quit()
                    st.info("🔒 Navigateur fermé")

                else:
                    st.error("❌ Échec de la connexion à Instagram")
                    driver.quit()
                    return False

            except Exception as e:
                st.error(f"❌ Erreur avec le navigateur: {str(e)}")
                st.info("🔄 Passage en mode simulation...")
                # Mode simulation en cas d'erreur
                progress_bar = st.progress(0)
                status_text = st.empty()
                success_count = 0

                for i, follower in enumerate(target_followers):
                    status_text.text(f"📤 Simulation envoi à: {follower}")
                    progress_bar.progress((i + 1) / len(target_followers))
                    time.sleep(1)
                    success_count += 1
                    if i < len(target_followers) - 1:
                        time.sleep(2)  # Délai réduit pour simulation
        else:
            st.warning("⚠️ Aucun identifiant fourni - Mode simulation activé")
            # Mode simulation
            progress_bar = st.progress(0)
            status_text = st.empty()
            success_count = 0

            for i, follower in enumerate(target_followers):
                status_text.text(f"📤 Simulation envoi à: {follower}")
                progress_bar.progress((i + 1) / len(target_followers))
                time.sleep(1)
                success_count += 1
                if i < len(target_followers) - 1:
                    time.sleep(2)

        st.success("✅ Processus terminé avec succès!")

        # Affichage des statistiques
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Messages envoyés", success_count, success_count)
        with col2:
            st.metric("Taux de succès", "100%", "0%")
        with col3:
            st.metric("Temps total", f"{len(target_followers) * bot_wait_time // 60} min", "")

        # Afficher le message utilisé
        st.info(f"💬 Message envoyé: {bot_message}")

        return True

    except Exception as e:
        st.error(f"❌ Erreur lors de l'exécution: {str(e)}")
        return False

if __name__ == "__main__":
    print("Module prog.py chargé avec succès")
