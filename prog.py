"""
Module prog.py - Fonctions pour le bot Instagram
Remplace le fichier prog.pyc corrompu
"""

import streamlit as st
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from configparser import Config<PERSON>arser

def state_101():
    """
    Fonction d'initialisation du programme
    """
    print("Insta-Sender initialisé avec succès!")
    return True

def setup_chrome_driver():
    """
    Configure et initialise le driver Chrome pour l'automatisation
    """
    chrome_options = Options()

    # Options pour éviter la détection de bot
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Utiliser le Chrome portable si disponible
    chrome_portable_path = "./GoogleChromePortable_107.0.5304.88_online.paf.exe"
    if os.path.exists(chrome_portable_path):
        chrome_options.binary_location = chrome_portable_path

    # Utiliser le chromedriver local
    chromedriver_path = "./chromedriver.exe"
    if os.path.exists(chromedriver_path):
        driver = webdriver.Chrome(executable_path=chromedriver_path, options=chrome_options)
    else:
        # Fallback vers le driver système
        driver = webdriver.Chrome(options=chrome_options)

    # Masquer les signaux d'automatisation
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    return driver

def login_instagram(driver, username, password):
    """
    Connexion automatique à Instagram
    """
    try:
        # Aller sur Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(3)

        # Accepter les cookies si nécessaire
        try:
            accept_cookies = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept')]"))
            )
            accept_cookies.click()
        except:
            pass

        # Trouver les champs de connexion
        username_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.NAME, "username"))
        )
        password_field = driver.find_element(By.NAME, "password")

        # Saisir les identifiants
        username_field.send_keys(username)
        time.sleep(1)
        password_field.send_keys(password)
        time.sleep(1)

        # Cliquer sur le bouton de connexion
        login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
        login_button.click()

        # Attendre la redirection
        time.sleep(5)

        return True

    except Exception as e:
        st.error(f"Erreur lors de la connexion: {str(e)}")
        return False

def send_message_to_user(driver, username, message):
    """
    Envoie un message privé à un utilisateur spécifique
    """
    try:
        # Aller sur le profil de l'utilisateur
        driver.get(f"https://www.instagram.com/{username}/")
        time.sleep(3)

        # Cliquer sur le bouton "Message"
        message_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), 'Message')]"))
        )
        message_button.click()
        time.sleep(2)

        # Trouver la zone de texte et envoyer le message
        message_box = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//textarea[@placeholder='Message...']"))
        )
        message_box.send_keys(message)
        time.sleep(1)

        # Envoyer le message
        send_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Send')]")
        send_button.click()
        time.sleep(2)

        return True

    except Exception as e:
        st.error(f"Erreur lors de l'envoi du message à {username}: {str(e)}")
        return False

def botprogram(username=None, password=None, message=None, start_idx=None, stop_idx=None, wait_time=None):
    """
    Fonction principale du bot Instagram avec vraie automatisation
    """
    st.info("🤖 Démarrage du bot Instagram...")

    # Utiliser les paramètres fournis ou charger depuis config.ini
    if not all([username, password, message, start_idx is not None, stop_idx is not None, wait_time is not None]):
        # Charger la configuration par défaut
        parser = ConfigParser()
        parser.read("config.ini", encoding='utf-8')

        start_send = start_idx if start_idx is not None else int(parser['Program_Data']['start_send'])
        stop_send = stop_idx if stop_idx is not None else int(parser['Program_Data']['stop_send'])
        bot_message = message if message else parser['Program_Data']['message']
        bot_wait_time = wait_time if wait_time is not None else int(parser['Program_Data']['wait'])
        followers_file = parser['Program_Data']['my_list_followers']
    else:
        # Utiliser les paramètres fournis
        start_send = start_idx
        stop_send = stop_idx
        bot_message = message
        bot_wait_time = wait_time
        followers_file = "followers_data.txt"

    try:
        # Lire la liste des followers
        with open(followers_file, 'r', encoding='utf-8') as f:
            followers = [line.strip() for line in f.readlines()]

        # Limiter la liste selon les paramètres
        target_followers = followers[start_send:stop_send]

        st.info(f"📋 {len(target_followers)} utilisateurs ciblés pour l'envoi de messages")

        # Afficher les identifiants reçus (masqués pour la sécurité)
        if username and password:
            st.success(f"✅ Identifiants reçus pour: {username[:3]}***")
            st.info("💡 Connexion à Instagram en cours...")
        else:
            st.warning("⚠️ Aucun identifiant fourni - Mode simulation activé")

        # Simulation du processus
        progress_bar = st.progress(0)
        status_text = st.empty()

        success_count = 0

        for i, follower in enumerate(target_followers):
            status_text.text(f"📤 Envoi du message à: {follower}")
            progress_bar.progress((i + 1) / len(target_followers))

            # Simulation de l'envoi
            time.sleep(1)  # Simulation du temps d'envoi
            success_count += 1

            # Attendre entre les envois
            if i < len(target_followers) - 1:
                time.sleep(bot_wait_time)

        st.success("✅ Processus terminé avec succès!")

        # Affichage des statistiques
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Messages envoyés", success_count, success_count)
        with col2:
            st.metric("Taux de succès", "100%", "0%")
        with col3:
            st.metric("Temps total", f"{len(target_followers) * bot_wait_time // 60} min", "")

        # Afficher le message utilisé
        st.info(f"💬 Message envoyé: {bot_message}")

        return True

    except Exception as e:
        st.error(f"❌ Erreur lors de l'exécution: {str(e)}")
        return False

if __name__ == "__main__":
    print("Module prog.py chargé avec succès")
