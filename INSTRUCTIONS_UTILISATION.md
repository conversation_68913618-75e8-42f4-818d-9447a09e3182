# 📋 Instructions d'Utilisation - Insta-Sender

## 🚀 Comment Utiliser Insta-Sender

### 1. Lancement de l'Application
```bash
streamlit run insta_sender.py
```

### 2. Interface Utilisateur

#### 📍 **Où Saisir vos Identifiants Instagram**

1. **Ouvrez votre navigateur** à l'adresse affichée (généralement `http://localhost:8501`)

2. **Dans la barre latérale**, sélectionnez : `⚙️ Insta-Sender FREE Version`

3. **Section "🔐 Identifiants Instagram"** :
   - **Nom d'utilisateur** : Saisissez votre nom d'utilisateur Instagram (sans @)
   - **Mot de passe** : Saisissez votre mot de passe Instagram

4. **Section "💬 Configuration du Message"** :
   - Personnalisez le message à envoyer
   - Par défaut : "*** هنا تضع رسالتك الخاصة بك ***"

5. **Paramètres d'Envoi** :
   - **Index de début** : Position de départ dans la liste (0-14)
   - **Index de fin** : Position de fin dans la liste (1-15)
   - **Délai entre envois** : Temps d'attente en secondes (10-60)

6. **Cliquez sur** : `🚀 Lancer InstaSender BOT`

### 3. Exemple de Saisie

```
📧 Nom d'utilisateur Instagram: mon_compte_insta
🔒 Mot de passe Instagram: ••••••••••••
💬 Message: Salut! J'ai vu ton profil et j'aimerais te suivre!
📊 Index début: 0
📊 Index fin: 5
⏱️ Délai: 15 secondes
```

### 4. Processus d'Exécution

Une fois lancé, le bot va :

1. **Initialiser** le navigateur Chrome
2. **Se connecter** à Instagram avec vos identifiants
3. **Charger** la liste des followers depuis `followers_data.txt`
4. **Envoyer** les messages un par un
5. **Attendre** le délai configuré entre chaque envoi
6. **Afficher** les statistiques finales

### 5. Fichiers de Configuration

#### `followers_data.txt`
```
hati.m9174
oussama.minos
koukatte
tahaalaam64
_ch_ahlam
```

#### `config.ini`
```ini
[Program_Data]
start_send = 0
stop_send = 15
message = *** هنا تضع رسالتك الخاصة بك ***
wait = 15
my_list_followers = followers_data.txt
```

### 6. Sécurité des Identifiants

- ✅ **Vos identifiants ne sont PAS sauvegardés**
- ✅ **Utilisés uniquement pour la session en cours**
- ✅ **Effacés à la fermeture de l'application**
- ⚠️ **Ne partagez jamais vos identifiants**

### 7. Limitations Version Gratuite

- **Maximum 15 followers** par session
- **Délai minimum** de 10 secondes entre envois
- **Pas de sauvegarde** des statistiques
- **Interface basique**

### 8. Dépannage

#### Problème : "Identifiants requis"
- **Solution** : Vérifiez que vous avez bien saisi nom d'utilisateur ET mot de passe

#### Problème : "Erreur de connexion"
- **Solution** : Vérifiez vos identifiants Instagram
- **Solution** : Vérifiez votre connexion Internet
- **Solution** : Désactivez temporairement l'authentification 2FA

#### Problème : "ChromeDriver non trouvé"
- **Solution** : Vérifiez que `chromedriver.exe` est dans le dossier
- **Solution** : Téléchargez la version compatible avec votre Chrome

#### Problème : "Liste de followers vide"
- **Solution** : Vérifiez que `followers_data.txt` contient des noms d'utilisateurs
- **Solution** : Un nom d'utilisateur par ligne, sans espaces

### 9. Conseils d'Utilisation

#### ✅ Bonnes Pratiques
- Commencez avec 3-5 followers pour tester
- Utilisez des délais de 15+ secondes
- Personnalisez vos messages
- Variez le contenu pour éviter la détection

#### ❌ À Éviter
- Envoyer trop de messages d'un coup
- Utiliser des délais trop courts (< 10 sec)
- Messages identiques répétitifs
- Utiliser sur des comptes importants sans test

### 10. Support

Pour toute question ou problème :
1. Vérifiez ce guide d'instructions
2. Consultez le fichier `CONNEXION_NAVIGATEUR.md`
3. Contactez le support via le formulaire dans l'application

---

## ⚠️ Avertissement Important

- **Respectez les conditions d'utilisation d'Instagram**
- **Utilisez à vos propres risques**
- **Testez d'abord avec un petit nombre de followers**
- **Ne spammez pas les utilisateurs**

---

*Insta-Sender v1.0 - Développé par Cyborg TEAM*
